%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2373897083767638746
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6390068798815842813}
  - component: {fileID: 4994550354499715071}
  m_Layer: 0
  m_Name: SceneContext
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6390068798815842813
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2373897083767638746}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4002292457670432566}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4994550354499715071
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2373897083767638746}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0da1ed04c5244af59e5d8521a1f2e78e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ObjectCache: {fileID: 0}
  Gameplay: {fileID: 0}
  Runner: {fileID: 0}
  LocalAgent: {fileID: 0}
--- !u!1001 &7937241976649755125
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6390068798815842813}
    m_Modifications:
    - target: {fileID: 6408840442287891512, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_Name
      value: ObjectCache
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
--- !u!4 &4002292457670432566 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6461610274493103811, guid: 020926b1f72360644b4a2adc36e98480, type: 3}
  m_PrefabInstance: {fileID: 7937241976649755125}
  m_PrefabAsset: {fileID: 0}
