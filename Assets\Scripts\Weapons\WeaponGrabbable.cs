using Projectiles;
using UnityEngine;

public class WeaponGrabbable : MonoBeh<PERSON>our
{
    [SerializeField]
    private Rigidbody weaponRigidbody;
    [SerializeField]
    private NetworkPhysicsGrabbable physicsGrabbable;
    [SerializeField]
    private Weapon weapon;
    [SerializeField]
    private WeaponAimVisual weaponAimVisual;



    [SerializeField,HideInInspector]
    private NetworkHand currentOwningHand;

    public void OnGrab()
    {

        currentOwningHand = null;

        transform.SetParent(physicsGrabbable.CurrentGrabber.transform);

        weaponAimVisual.TurnON();


        if (physicsGrabbable.CurrentGrabber)
        {
            physicsGrabbable.CurrentGrabber.hand.currentWeapon = weapon;
            currentOwningHand = physicsGrabbable.CurrentGrabber.hand;
        }
        else
        {
            Debug.Log("@@@@@@@@ Netowrk Grabber NOT FOUND!!");
        }



    }

    public void OnUnGrab()
    {
        if (currentOwningHand)
        {    
            weaponAimVisual.TurnOFF();
            var weapon = currentOwningHand.currentWeapon;
            if (weapon != null)
            {
                weapon.SetWeaponContext(null, EquippedHand.none);
                weapon.DisarmWeapon();
                currentOwningHand.currentWeapon = null;
                currentOwningHand = null;            
            }
            else
            {
                Debug.LogError("@@@@@@@@ Current weapon is null !!");
            }

        }
        else
        {
            Debug.Log("@@@@@@@@ Current nework hand is null !!");
        }

        transform.SetParent(null);
    }
}
