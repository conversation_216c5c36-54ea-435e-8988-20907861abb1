using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;

public abstract class Grabbable : MonoBehaviour
{
    public Grabber currentGrabber;
    [HideInInspector]
    public NetworkGrabbable networkGrabbable = null;
    [HideInInspector]
    public Vector3 localPositionOffset;
    [HideInInspector]
    public Quaternion localRotationOffset;
    [HideInInspector]
    public Vector3 ungrabPosition;
    [HideInInspector]
    public Quaternion ungrabRotation;
    [HideInInspector]
    public Vector3 ungrabVelocity;
    [HideInInspector]
    public Vector3 ungrabAngularVelocity;
    public abstract Vector3 Velocity { get; }

    public abstract Vector3 AngularVelocity { get; }

    public bool isGrabbed = false;

    [Header("Grabbed Position Offset")]
    public Vector3 rightHandPositionOffset;
    public Vector3 leftHandPositionOffset;
    [Header("Grabbed Rotation Offset ")]
    public Quaternion rightHandRotationOffset; // Stored in Euler angles
    public Quaternion leftHandRotationOffset;  // Stored in Euler angles

    [SerializeField]
    protected GameObject grabberHighlight;

    private void Awake()
    {
        HideGrabberHighlight();
    }

    public virtual bool Grab(Grabber newGrabber)
    {
        if (isGrabbed) return false;
        // Find grabbable position/rotation in grabber referential
        localPositionOffset = newGrabber.transform.InverseTransformPoint(transform.position);
        localRotationOffset = Quaternion.Inverse(newGrabber.transform.rotation) * transform.rotation;
        currentGrabber = newGrabber;
        isGrabbed = true;
        HideGrabberHighlight();
        return true;
    }



    public virtual void Ungrab()
    {
        currentGrabber = null;
        if (networkGrabbable)
        {
            ungrabPosition = networkGrabbable.transform.position;
            ungrabRotation = networkGrabbable.transform.rotation;
            ungrabVelocity = Velocity;
            ungrabAngularVelocity = AngularVelocity;
        }
        isGrabbed = false;
        HideGrabberHighlight();
    }

    public virtual void ShowGrabberHighlight()
    {
        if (grabberHighlight)
        {
            grabberHighlight.SetActive(true);
        }


    }

    public virtual void HideGrabberHighlight()
    {
        if (grabberHighlight)
        {
            grabberHighlight.SetActive(false);
        }
    }
}

