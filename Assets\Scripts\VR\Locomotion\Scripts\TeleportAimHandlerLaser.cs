/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * Licensed under the Oculus SDK License Agreement (the "License");
 * you may not use the Oculus SDK except in compliance with the License,
 * which is provided at the time of installation or download, or which
 * otherwise accompanies this software in either electronic or hard copy form.
 *
 * You may obtain a copy of the License at
 *
 * https://developer.oculus.com/licenses/oculussdk/
 *
 * Unless required by applicable law or agreed to in writing, the Oculus SDK
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Meta.XR.Samples;

[MetaCodeSample("StarterSample.Core-Locomotion")]
public class TeleportAimHandlerLaser : TeleportAimHandler
{
    /// <summary>
    /// Maximum range for aiming.
    /// </summary>
    [Tooltip("Maximum range for aiming.")]
    public float Range = 100;

    /// <summary>
    /// Return the set of points that represent the aiming line.
    /// </summary>
    /// <param name="points"></param>
    public override void GetPoints(List<Vector3> points)
    {
        Ray aimRay;
        LocomotionTeleport.InputHandler.GetAimData(out aimRay);
        points.Add(aimRay.origin);
        points.Add(aimRay.origin + aimRay.direction * Range);
    }
}
