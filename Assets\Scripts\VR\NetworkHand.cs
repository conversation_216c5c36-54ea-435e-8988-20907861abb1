using UnityEngine;
using Fusion;
using Projectiles;
using System;


[DefaultExecutionOrder(NetworkGrabber.EXECUTION_ORDER)]
public class NetworkHand : NetworkBehaviour
{
    public const int EXECUTION_ORDER = PlayerAgent.EXECUTION_ORDER + 10;

    [HideInInspector]
    public PlayerAgent playerAgent;
    public bool isGrabbing;

    public Weapon currentWeapon;

    // Track if the grab button was released or pressed this frame
    private bool wasGrabbingLastFrame;
    public bool GrabButtonJustReleased { get; private set; }
    public bool GrabButtonJustPressed { get; private set; }

    public NetworkGrabber networkGrabber;

    public RigPart side;
    [SerializeField]
    private Animator animator;
    [SerializeField,Tooltip("This will be a empty transform, attached to hand mesh, which position we will use to spawn objects into")]
    private Transform objectSpawnPoint;
    public Transform ObjectSpawnPoint
    {
        get
        {
            if (objectSpawnPoint == null)
            {
              return  transform;
            }
            else
                return objectSpawnPoint;
        }
    }
    // Cache hashes for animation parameters
    private static readonly int HandOpenAnim = Animator.StringToHash("OpenEmpty");
    private static readonly int HandClosedEquippedAnim = Animator.StringToHash("ClosedEquipped");
    private static readonly int HandClosedEmptyAnim = Animator.StringToHash("ClosedEmpty");


    public bool IsLocalNetworkRig => playerAgent.IsLocalNetworkPlayer;

    private void Awake()
    {
        playerAgent = GetComponentInParent<PlayerAgent>();


        Debug.Assert(networkGrabber, "ASSIGN NETWORK GRABBER TO NETWORK HAND " + this.name);
        Debug.Assert(animator, "ASSIGN ANIMATOR TO NETWORK HAND " + this.name);

    }



    public override void Spawned()
    {
        base.Spawned();

        // Initialize button state tracking
        wasGrabbingLastFrame = false;
        GrabButtonJustReleased = false;
        GrabButtonJustPressed = false;
    }

    public override void FixedUpdateNetwork()
    {
        base.FixedUpdateNetwork();

        // Check for button state changes
        if (Object.HasInputAuthority)
        {
            // Update button state changes
            GrabButtonJustReleased = wasGrabbingLastFrame && !isGrabbing;
            GrabButtonJustPressed = !wasGrabbingLastFrame && isGrabbing;

            // Store current state for next frame
            wasGrabbingLastFrame = isGrabbing;
        }
    }

    /// <summary>
    /// Checks if the grab button is currently being held down
    /// </summary>
    /// <returns>True if the grab button is being held down</returns>
    public bool IsGrabButtonHeld()
    {
        return isGrabbing;
    }

    /// <summary>
    /// Checks if the grab button was just released this frame
    /// </summary>
    /// <returns>True if the grab button was released this frame</returns>
    public bool IsGrabButtonReleased()
    {
        return GrabButtonJustReleased;
    }

    /// <summary>
    /// Checks if the grab button was just pressed this frame
    /// </summary>
    /// <returns>True if the grab button was pressed this frame</returns>
    public bool IsGrabButtonPressed()
    {
        return GrabButtonJustPressed;
    }

    public void TurnOffHandMesh()
    {
        animator.gameObject.SetActive(false);
    }
    public void TurnOnHandMesh()
    {
        animator.gameObject.SetActive(true);
    }

    /// <summary>
    /// Players Hand is in Default Open Pose
    /// </summary>
    public void SetHandOpen()
    {
        animator.SetBool(HandOpenAnim, true);
        animator.SetBool(HandClosedEquippedAnim, false);
        animator.SetBool(HandClosedEmptyAnim, false);
    }

    /// <summary>
    /// Players hand is closed and holding onto a object, e.g. Equipped Weapon
    /// </summary>
    public void SetHandClosed()
    {
        animator.SetBool(HandClosedEquippedAnim, true);
        animator.SetBool(HandOpenAnim, false);
        animator.SetBool(HandClosedEmptyAnim, false);
    }

    /// <summary>
    /// Players has is closed and not holding anything
    /// </summary>
    public void SetHandClosedEmpty()
    {
        animator.SetBool(HandClosedEmptyAnim, true);
        animator.SetBool(HandClosedEquippedAnim, false);
        animator.SetBool(HandOpenAnim, false);
    }
}
