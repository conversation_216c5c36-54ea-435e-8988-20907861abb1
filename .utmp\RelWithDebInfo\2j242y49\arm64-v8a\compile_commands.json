[{"directory": "C:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/2j242y49/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.47F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android32 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -IC:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity -isystem C:/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAApplication.cpp.o -c C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAApplication.cpp", "file": "C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAApplication.cpp"}, {"directory": "C:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/2j242y49/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.47F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android32 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -IC:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity -isystem C:/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAConfiguration.cpp.o -c C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAConfiguration.cpp", "file": "C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAConfiguration.cpp"}, {"directory": "C:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/2j242y49/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.47F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android32 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -IC:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity -isystem C:/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGADebug.cpp.o -c C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGADebug.cpp", "file": "C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGADebug.cpp"}, {"directory": "C:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/2j242y49/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.47F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android32 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -IC:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity -isystem C:/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAEntry.cpp.o -c C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAEntry.cpp", "file": "C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAEntry.cpp"}, {"directory": "C:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/2j242y49/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.47F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android32 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -IC:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity -isystem C:/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInput.cpp.o -c C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInput.cpp", "file": "C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInput.cpp"}, {"directory": "C:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/2j242y49/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.47F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android32 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -IC:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity -isystem C:/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInputKeyEvent.cpp.o -c C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputKeyEvent.cpp", "file": "C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputKeyEvent.cpp"}, {"directory": "C:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/2j242y49/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.47F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android32 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -IC:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity -isystem C:/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInputMotionEvent.cpp.o -c C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputMotionEvent.cpp", "file": "C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputMotionEvent.cpp"}, {"directory": "C:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/2j242y49/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.47F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android32 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -IC:/Users/<USER>/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity -isystem C:/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGASoftKeyboard.cpp.o -c C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGASoftKeyboard.cpp", "file": "C:\\Users\\<USER>\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGASoftKeyboard.cpp"}]