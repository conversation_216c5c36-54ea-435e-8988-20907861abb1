using UnityEngine;
using Fusion;
using Projectiles;

public class WeaponAimVisual : MonoBeh<PERSON>our
{
    [SerializeField]
    private Weapon weapon;
    
    [SerializeField]
    private Transform weaponBarrel;

    [Header("Visual Toggles")]
    [SerializeField]
    private bool showCrosshair = true;
    [SerializeField]
    private bool showAimLine = true;
    [SerializeField]
    private bool isAimVisualEnabled = false;

    [Header("Crosshair Settings")]
    [SerializeField]
    private GameObject crosshairPrefab;
    [SerializeField]
    private float crosshairSize = 0.1f;
    [SerializeField]
    private float maxDistance = 50f;
    [SerializeField]
    private LayerMask hitLayers;
    // [SerializeField]
    // private int updateFrameInterval = 2;

    [Header("Aim Line Settings")]
    [SerializeField]
    private LineRenderer aimLine;
    [SerializeField]
    private Color aimLineColor = Color.red;
    [SerializeField]
    private float lineWidth = 0.02f;

    private GameObject activeCrosshair;
    private int frameCount;

    // Public property to check if aim visual is enabled
    public bool IsAimVisualEnabled => isAimVisualEnabled;

    private void Awake()
    {
        if (crosshairPrefab != null && showCrosshair)
        {
            activeCrosshair = Instantiate(crosshairPrefab);
            activeCrosshair.transform.localScale = Vector3.one * crosshairSize;
            activeCrosshair.SetActive(false);
        }

        if (aimLine != null)
        {
            aimLine.startWidth = lineWidth;
            aimLine.endWidth = lineWidth;
            aimLine.material.color = aimLineColor;
            aimLine.positionCount = 2;
            aimLine.enabled = false;
        }
    }

    public void TurnOFF()
    {
        isAimVisualEnabled = false;

        if (activeCrosshair != null)
            activeCrosshair.SetActive(false);

        if (aimLine != null)
            aimLine.enabled = false;

      
    }

    public void TurnON()
    {
        isAimVisualEnabled = true;
    
    }

    private void OnDestroy()
    {
        if (activeCrosshair != null)
            Destroy(activeCrosshair);
    }

    private void LateUpdate()
    {
        frameCount++;
       // if (frameCount % updateFrameInterval == 0)
       // {
            UpdateAimVisuals();
// }
    }

    void UpdateAimVisuals()
    {
        bool shouldShow = isAimVisualEnabled && weapon.owningPlayer != null && weapon.owningPlayer.IsLocalNetworkPlayer;

        if (!shouldShow)
        {
            if (activeCrosshair != null) activeCrosshair.SetActive(false);
            if (aimLine != null) aimLine.enabled = false;
            return;
        }

        Vector3 startPoint = weaponBarrel.position;
        Vector3 endPoint = startPoint + (weaponBarrel.forward * maxDistance);

        if (ProjectileUtility.ProjectileCast(weapon.Runner, weapon.Object.InputAuthority, 
            startPoint, weaponBarrel.forward, maxDistance, hitLayers, out LagCompensatedHit hit))
        {
            endPoint = hit.Point;

            if (activeCrosshair != null && showCrosshair)
            {
                activeCrosshair.SetActive(true);
                activeCrosshair.transform.position = hit.Point;
                activeCrosshair.transform.rotation = Quaternion.LookRotation(-hit.Normal);
                activeCrosshair.transform.position += hit.Normal * 0.001f;
            }
        }
        else
        {
            if (activeCrosshair != null)
                activeCrosshair.SetActive(false);
        }

        if (aimLine != null && showAimLine)
        {
            aimLine.enabled = true;
            Vector3 newStartPoint = new Vector3(startPoint.x, startPoint.y - 0.05f, startPoint.z);
            aimLine.SetPosition(0, newStartPoint);
            aimLine.SetPosition(1, endPoint);
        }
        else if (aimLine != null)
        {
            aimLine.enabled = false;
        }
    }

    // Public methods to toggle visuals at runtime if needed
    public void SetCrosshairVisibility(bool visible)
    {
        showCrosshair = visible;
        if (!visible && activeCrosshair != null)
            activeCrosshair.SetActive(false);
    }

    public void SetAimLineVisibility(bool visible)
    {
        showAimLine = visible;
        if (!visible && aimLine != null)
            aimLine.enabled = false;
    }
}
