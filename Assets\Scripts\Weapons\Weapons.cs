using System.Collections.Generic;
using System.Linq;
using Fusion;
using UnityEngine;

namespace Projectiles
{
    public class WeaponContext
    {
        // We do not want to link AgentInput directly in case the weapon is used
        // by other entities (e.g. turret, NPC)
        public NetworkButtons Buttons;
        public NetworkButtons PressedButtons;
        public Vector3 MoveVelocity;

        public Transform FireTransform;
        public HitscanProjectileBuffer HitscanProjectiles;
        public KinematicProjectileBuffer KinematicProjectiles;
    }

    /// <summary>
    /// Handles player weapons, supporting dual-wielding.
    /// </summary>
    [DefaultExecutionOrder(5)]
    public class Weapons : ContextBehaviour
    {
        // PUBLIC MEMBERS

        // Dual-wielding properties
        public bool IsSwitchingWeaponLeft => _switchCooldownLeft.ExpiredOrNotRunning(Runner) == false;
        public bool IsSwitchingWeaponRight => _switchCooldownRight.ExpiredOrNotRunning(Runner) == false;
        public float ElapsedSwitchTimeLeft => _weaponSwitchDuration - _switchCooldownLeft.RemainingTime(Runner).GetValueOrDefault();
        public float ElapsedSwitchTimeRight => _weaponSwitchDuration - _switchCooldownRight.RemainingTime(Runner).GetValueOrDefault();

        public NetworkHand rightNetworkHand;
        public NetworkHand leftNetworkHand;

        public Weapon CurrentLeftWeapon => leftNetworkHand.currentWeapon;
        public Weapon PendingLeftWeapon => _leftWeapons[PendingLeftWeaponSlot];
        public Weapon CurrentRightWeapon => rightNetworkHand.currentWeapon;
        public Weapon PendingRightWeapon => _rightWeapons[PendingRightWeaponSlot];

        [Networked]
        public int CurrentLeftWeaponSlot { get; private set; }
        [Networked, HideInInspector]
        public int PendingLeftWeaponSlot { get; private set; }
        [Networked]
        public int CurrentRightWeaponSlot { get; private set; }
        [Networked, HideInInspector]
        public int PendingRightWeaponSlot { get; private set; }

        public int Version => _version;

        // PRIVATE MEMBERS
        [Networked, Capacity(12)]
        private NetworkArray<Weapon> _rightWeapons { get; }
        [Networked, Capacity(12)]
        private NetworkArray<Weapon> _leftWeapons { get; }
        [Networked]
        private TickTimer _switchCooldownLeft { get; set; }
        [Networked]
        private TickTimer _switchCooldownRight { get; set; }

        [SerializeField]
        private Weapon[] _initialWeapons;

        bool setRightHandEmpty;
        bool setLeftHandEmpty;


        [Header("Right Weapon")]
        [SerializeField]
        private Transform _weaponsRootRight; // Right hand weapons root
        [SerializeField]
        private Transform _fireTransformRight; // Fire transform for right hand
        [SerializeField]
        private Vector3 _firstPersonWeaponOffsetRight = new(-0.15f, 0f, 0f);

        [Header("Left Weapon")]
        [SerializeField]
        private Transform _weaponsRootLeft;  // Left hand weapons root
        [SerializeField]
        private Transform _fireTransformLeft;  // Fire transform for left hand
        [SerializeField]
        private Vector3 _firstPersonWeaponOffsetLeft = new(0.15f, 0f, 0f); // Adjust offset for left hand

        [Header("Weapon Switch")]
        [SerializeField]
        private float _weaponSwitchDuration = 1f;
        [SerializeField, Tooltip("When the actual weapon swap happens during weapon switch")]
        private float _weaponSwapTime = 0.5f;



        private int _version;
        private PlayerAgent _agent;
        private WeaponContext _weaponContextRight = new();
        private WeaponContext _weaponContextLeft = new();

        // PUBLIC METHODS

        /// <summary>
        /// Switches weapon for the right or left hand.
        /// </summary>
        public void SwitchWeapon(int weaponSlot, bool immediate, bool isLeftHand, NetworkArray<Weapon> weaponList)
        {
            if (weaponSlot < 0 || weaponSlot >= weaponList.Length)
                return;

            var weapon = weaponList[weaponSlot];
            if (weapon == null)
                return;

            if (isLeftHand)
            {
                if (immediate || _weaponSwitchDuration <= 0f)
                {
                    PendingLeftWeaponSlot = weaponSlot;
                    CurrentLeftWeaponSlot = weaponSlot;
                    _switchCooldownLeft = default;

                    if (weaponSlot > 0 && _agent.LeftNetworkHand())
                        _agent.LeftNetworkHand().SetHandClosed();

                }
                else
                {
                    StartWeaponSwitchLeft(weaponSlot);
                }

                setLeftHandEmpty = false;
            }
            else
            {
                if (immediate || _weaponSwitchDuration <= 0f)
                {
                    PendingRightWeaponSlot = weaponSlot;
                    CurrentRightWeaponSlot = weaponSlot;
                    _switchCooldownRight = default;

                    if (weaponSlot > 0 && _agent.RightNetworkHand())
                        _agent.RightNetworkHand().SetHandClosed();
                }
                else
                {
                    StartWeaponSwitchRight(weaponSlot);
                }
                setRightHandEmpty = false;
            }

        }

        public void SwitchWeaponRightHand(int weaponSlot, bool immediate)
        {

            NetworkArray<Weapon> weaponList = _rightWeapons;

            if (weaponSlot < 0 || weaponSlot >= weaponList.Length)
                return;

            //If our desired weapon slot is 0. This means we want to remove weapon from hands and just use hands.
            if (weaponSlot.Equals(0))
            {
                SetRightHandEmpty();

                PendingRightWeaponSlot = 0;
                CurrentRightWeaponSlot = 0;
                _switchCooldownRight = default;


                return;
            }

            var weapon = weaponList[weaponSlot];

            if (weapon == null)
                return;

            setRightHandEmpty = false;

            if (immediate || _weaponSwitchDuration <= 0f)
            {
                PendingRightWeaponSlot = weaponSlot;
                CurrentRightWeaponSlot = weaponSlot;
                _switchCooldownRight = default;


                if (_agent.RightNetworkHand())
                    _agent.RightNetworkHand().SetHandClosed();

            }
            else
            {
                StartWeaponSwitchRight(weaponSlot);
            }

        }


        public void SwitchWeaponLeftHand(int weaponSlot, bool immediate)
        {

            NetworkArray<Weapon> weaponList = _leftWeapons;

            if (weaponSlot < 0 || weaponSlot >= weaponList.Length)
                return;

            //If our desired weapon slot is 0. This means we want to remove weapon from hands and just use hands.
            if (weaponSlot.Equals(0))
            {
                SetLeftHandEmpty();

                PendingLeftWeaponSlot = 0;
                CurrentLeftWeaponSlot = 0;
                _switchCooldownLeft = default;
                return;
            }

            var weapon = weaponList[weaponSlot];
            if (weapon == null)
                return;

            setLeftHandEmpty = false;
            if (immediate || _weaponSwitchDuration <= 0f)
            {
                PendingLeftWeaponSlot = weaponSlot;
                CurrentLeftWeaponSlot = weaponSlot;
                _switchCooldownLeft = default;

                if (_agent.LeftNetworkHand())
                    _agent.LeftNetworkHand().SetHandClosed();
            }
            else
            {
                StartWeaponSwitchLeft(weaponSlot);
            }

        }
        /// <summary>
        /// Gets the next weapon slot for the specified hand.
        /// </summary>
        public int GetNextWeaponSlot(int fromSlot, NetworkArray<Weapon> weaponList, bool ignoreZeroWeapon = false)
        {
            int weaponsLength = weaponList.Length;

            for (int i = 0; i < weaponsLength; i++)
            {
                int slot = (fromSlot + i + 1) % weaponsLength;

                if (slot == 0 && ignoreZeroWeapon)
                    continue;

                if (weaponList[slot] != null)
                    return slot;
            }

            return 0;
        }

        /// <summary>
        /// Gets the previous weapon slot for the specified hand.
        /// </summary>
        public int GetPreviousWeaponSlot(int fromSlot, NetworkArray<Weapon> weaponList, bool ignoreZeroWeapon = false)
        {
            int weaponsLength = weaponList.Length;

            for (int i = 0; i < weaponsLength; i++)
            {
                int slot = (weaponsLength + fromSlot - i - 1) % weaponsLength;

                if (slot == 0 && ignoreZeroWeapon)
                    continue;

                if (weaponList[slot] != null)
                    return slot;
            }

            return 0;
        }

        public void GetAllWeapons(List<Weapon> weapons)
        {
            for (int i = 0; i < _rightWeapons.Length; i++)
            {
                if (_rightWeapons[i] != null)
                {
                    weapons.Add(_rightWeapons[i]);
                }
            }
        }

        // NetworkBehaviour INTERFACE

        public override void Spawned()
        {
            if (HasStateAuthority == false)
            {
                RefreshWeapons();

                if (IsRightHandEmpty())
                    SetRightHandEmpty();

                if (IsLeftHandEmpty())
                    SetLeftHandEmpty();
                return;
            }



            // // Spawn initial weapons for left and right hands
            // for (int i = 0; i < _initialWeapons.Length; i++)
            // {


            //     var weaponPrefabRight = _initialWeapons[i];
            //     if (weaponPrefabRight == null)
            //         continue;

            //     var weaponRight = Runner.Spawn(weaponPrefabRight, inputAuthority: Object.InputAuthority);
            //     AddWeapon(weaponRight, _rightWeapons);

            //     var weaponPrefabLeft = _initialWeapons[i];
            //     if (weaponPrefabLeft == null)
            //         continue;
            //     var weaponLeft = Runner.Spawn(weaponPrefabLeft, inputAuthority: Object.InputAuthority);
            //     AddWeapon(weaponLeft, _leftWeapons);





            // }

            // // Equip first weapons to both hands
            // // Assign first available weapon to right hand
            // SwitchWeapon(CurrentRightWeaponSlot, true, isLeftHand: false, _rightWeapons);

            // SwitchWeapon(CurrentLeftWeaponSlot, true, isLeftHand: true, _leftWeapons);




            RefreshWeapons();

            if (IsRightHandEmpty())
                SetRightHandEmpty();
            else
                setRightHandEmpty = false;

            if (IsLeftHandEmpty())
                SetLeftHandEmpty();
            else
                setLeftHandEmpty = false;


        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            try
            {
                // Skip cleanup during scene transitions to avoid "spawned and despawned in the same tick" issues
                if (runner.SceneManager != null && runner.SceneManager.IsBusy)
                {
                    Debug.Log("Skipping weapon cleanup during scene transition");
                    return;
                }

                // Check if we have valid NetworkArrays before proceeding
                if (!Object.IsValid)
                {
                    Debug.LogWarning("NetworkObject is not valid during Despawned, skipping weapon cleanup");
                    return;
                }

                // Cleanup weapons
                try
                {
                    // Safely access _rightWeapons
                    try
                    {
                        int length = _rightWeapons.Length;
                        for (int i = 0; i < length; i++)
                        {
                            try
                            {
                                var weapon = _rightWeapons[i];
                                if (weapon != null && weapon.Object != null && weapon.Object.IsValid)
                                {
                                    RemoveWeapon(weapon.WeaponSlot, _rightWeapons, true);
                                }
                            }
                            catch (System.Exception ex)
                            {
                                Debug.LogWarning($"Error cleaning up right weapon at index {i}: {ex.Message}");
                            }
                        }
                    }
                    catch (System.NullReferenceException)
                    {
                        Debug.LogWarning("_rightWeapons is null or not accessible");
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"Error accessing _rightWeapons: {ex.Message}");
                }

                try
                {
                    // Safely access _leftWeapons
                    try
                    {
                        int length = _leftWeapons.Length;
                        for (int i = 0; i < length; i++)
                        {
                            try
                            {
                                var weapon = _leftWeapons[i];
                                if (weapon != null && weapon.Object != null && weapon.Object.IsValid)
                                {
                                    RemoveWeapon(weapon.WeaponSlot, _leftWeapons, true);
                                }
                            }
                            catch (System.Exception ex)
                            {
                                Debug.LogWarning($"Error cleaning up left weapon at index {i}: {ex.Message}");
                            }
                        }
                    }
                    catch (System.NullReferenceException)
                    {
                        Debug.LogWarning("_leftWeapons is null or not accessible");
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"Error accessing _leftWeapons: {ex.Message}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Error in Weapons.Despawned: {ex.Message}");
            }
        }

        public override void FixedUpdateNetwork()
        {
            ProcessInput();
            UpdateWeaponSwitchLeft();
            UpdateWeaponSwitchRight();





        }

        public override void Render()
        {

            if (IsRightHandEmpty() && !setRightHandEmpty)
            {
                SetRightHandEmpty();

            }



            if (IsLeftHandEmpty() && !setLeftHandEmpty)
            {
                SetLeftHandEmpty();

            }





          //  int layerFirstPerson = HasInputAuthority ? ObjectLayer.FirstPerson : ObjectLayer.ThirdPerson;

            //Vector3 offsetRight = HasInputAuthority ? _firstPersonWeaponOffsetRight : Vector3.zero;
            //Vector3 offsetLeft = HasInputAuthority ? _firstPersonWeaponOffsetLeft : Vector3.zero;

            RefreshWeapons();
          //  SetWeaponViewRight(layerFirstPerson, _firstPersonWeaponOffsetRight);
          //  SetWeaponViewLeft(layerFirstPerson, _firstPersonWeaponOffsetLeft);
        }

        // MONOBEHAVIOUR

        protected void Awake()
        {
            _agent = GetComponent<PlayerAgent>();

            var hitScanBuffers = GetComponents<HitscanProjectileBuffer>();


            _weaponContextRight.KinematicProjectiles = GetComponent<KinematicProjectileBuffer>();
            _weaponContextRight.HitscanProjectiles = hitScanBuffers[0];
            _weaponContextRight.FireTransform = _fireTransformRight;

            _weaponContextLeft.KinematicProjectiles = GetComponent<KinematicProjectileBuffer>();
            _weaponContextLeft.HitscanProjectiles = hitScanBuffers[1];
            _weaponContextLeft.FireTransform = _fireTransformLeft;
        }

        // PRIVATE METHODS

        private void ProcessInput()
        {
            if (IsProxy)
                return;

            // Handle input for right hand
            if (CurrentRightWeapon != null)
            {
                if (GetInput(out CombinedInput inputRight))
                {
                    // Assuming input has separate fields for left and right hand
                    // Modify this according to your input structure

                    // Example: inputRight.gameplayInput.RightWeaponSlot
                    //SwitchWeapon(inputRight.gameplayInput.RightWeaponSlot, false, isLeftHand: false, _rightWeapons);

                    if (!IsSwitchingWeaponRight)
                    {
                        _weaponContextRight.Buttons = inputRight.gameplayInput.ButtonsRight;
                        _weaponContextRight.PressedButtons = inputRight.gameplayInput.ButtonsRight.GetPressed(_agent.Input.PreviousButtonsRight);
                        // _weaponContextRight.MoveVelocity = _agent.KCC.RealVelocity;

                        if (CurrentRightWeapon.ProcessFireInput(_weaponContextRight))
                        {

                            _agent.Health.StopImmortality();
                        }
                    }
                }
            }

            // Handle input for left hand
            if (CurrentLeftWeapon != null)
            {
                if (GetInput(out CombinedInput inputLeft))
                {
                    // Assuming input has separate fields for left and right hand
                    // Modify this according to your input structure

                    // Example: inputLeft.gameplayInput.LeftWeaponSlot
                    //  SwitchWeapon(inputLeft.gameplayInput.LeftWeaponSlot, false, isLeftHand: true, _leftWeapons);

                    if (!IsSwitchingWeaponLeft)
                    {
                        _weaponContextLeft.Buttons = inputLeft.gameplayInput.ButtonsLeft;
                        _weaponContextLeft.PressedButtons = inputLeft.gameplayInput.ButtonsLeft.GetPressed(_agent.Input.PreviousButtonsLeft);
                        // _weaponContextLeft.MoveVelocity = _agent.KCC.RealVelocity;

                        if (CurrentLeftWeapon.ProcessFireInput(_weaponContextLeft))
                        {

                            _agent.Health.StopImmortality();
                        }
                    }
                }
            }
        }

        private void StartWeaponSwitchLeft(int weaponSlot)
        {
            if (weaponSlot == PendingLeftWeaponSlot)
                return;

            PendingLeftWeaponSlot = weaponSlot;

            if (ElapsedSwitchTimeLeft < _weaponSwapTime)
                return; // We haven't swapped weapon yet, just continue with new pending weapon

            if (weaponSlot > 0 && _agent.LeftNetworkHand())
                _agent.LeftNetworkHand().SetHandClosed();

            _switchCooldownLeft = TickTimer.CreateFromSeconds(Runner, _weaponSwitchDuration);
        }

        private void StartWeaponSwitchRight(int weaponSlot)
        {
            if (weaponSlot == PendingRightWeaponSlot)
                return;

            PendingRightWeaponSlot = weaponSlot;

            if (ElapsedSwitchTimeRight < _weaponSwapTime)
                return; // We haven't swapped weapon yet, just continue with new pending weapon

            if (weaponSlot > 0 && _agent.RightNetworkHand())
                _agent.RightNetworkHand().SetHandClosed();

            _switchCooldownRight = TickTimer.CreateFromSeconds(Runner, _weaponSwitchDuration);
        }

        private void UpdateWeaponSwitchLeft()
        {
            if (IsProxy)
                return;

            if (CurrentLeftWeaponSlot == PendingLeftWeaponSlot)
                return;

            if (ElapsedSwitchTimeLeft < _weaponSwapTime)
                return;

            if (GetInput(out CombinedInput inputRight))
            {
                if (inputRight.gameplayInput.ButtonsLeft.WasPressed(_agent.Input.PreviousButtonsLeft, 4))
                {

                }

            }





            CurrentLeftWeaponSlot = PendingLeftWeaponSlot;
        }

        private void UpdateWeaponSwitchRight()
        {
            if (IsProxy)
                return;

            if (CurrentRightWeaponSlot == PendingRightWeaponSlot)
                return;

            if (ElapsedSwitchTimeRight < _weaponSwapTime)
                return;

            CurrentRightWeaponSlot = PendingRightWeaponSlot;
        }

        private void RefreshWeapons()
        {
            RefreshWeapon(CurrentRightWeapon, EquippedHand.right, _weaponsRootRight, _weaponContextRight, _rightWeapons);
            RefreshWeapon(CurrentLeftWeapon, EquippedHand.left, _weaponsRootLeft, _weaponContextLeft, _leftWeapons);
            _version++;
        }



        public bool IsRightHandEmpty()
        {
            return CurrentRightWeapon == null;
        }

        public bool IsLeftHandEmpty()
        {
            return CurrentLeftWeapon == null;
        }

        public void SetRightHandEmpty()
        {
            foreach (var weapon in _rightWeapons)
            {
                if (weapon != null)
                {
                    weapon.DisarmWeapon();
                    weapon.SetActive(false);
                }
            }

            if (_agent.RightNetworkHand())
                _agent.RightNetworkHand().SetHandOpen();

            setRightHandEmpty = true;
        }

        public void SetLeftHandEmpty()
        {
            foreach (var weapon in _leftWeapons)
            {
                if (weapon != null)
                {
                    weapon.DisarmWeapon();
                    weapon.SetActive(false);
                }
            }

            if (_agent.LeftNetworkHand())
                _agent.LeftNetworkHand().SetHandOpen();

            setLeftHandEmpty = true;
        }


        private void RefreshWeapon(Weapon currentWeapon, EquippedHand equippedHand, Transform weaponsRoot, WeaponContext weaponContext, NetworkArray<Weapon> weaponList)
        {




            if (currentWeapon == null)
                return;

            if (currentWeapon.IsArmed)
                return; // Proper weapon is ready


            if (currentWeapon)
                currentWeapon.SetWeaponContext(weaponContext, equippedHand);

            if (_agent)
                currentWeapon.SetOwner(_agent);

            // foreach (var weapon in weaponList)
            // {
            //     if (weapon == null)
            //         continue;

            //     if (weapon == currentWeapon)
            //         weapon.SetWeaponContext(weaponContext, equippedHand);

            //     if (_agent)
            //         weapon.SetOwner(_agent);

            //     if (weapon != currentWeapon /*&& weapon != currentOtherWeapon*/ )
            //     {
            //         weapon.DisarmWeapon();
            //         weapon.SetActive(false);
            //     }
            // }

            // currentWeapon.transform.SetParent(weaponsRoot, false);
            // currentWeapon.SetActive(true);

            currentWeapon.ArmWeapon();

            if (weaponContext.HitscanProjectiles != null)
            {
                weaponContext.HitscanProjectiles.UpdateBarrelTransforms(currentWeapon.BarrelTransforms);
            }

            if (weaponContext.KinematicProjectiles != null)
            {
                weaponContext.KinematicProjectiles.UpdateBarrelTransforms(currentWeapon.BarrelTransforms);
            }

            if (equippedHand == EquippedHand.left)
                setLeftHandEmpty = false;

            if (equippedHand == EquippedHand.right)
                setRightHandEmpty = false;

        }


        private void SetWeaponViewRight(int layer, Vector3 offset)
        {
            if (CurrentRightWeapon == null)
                return;

            var currentWeapon = CurrentRightWeapon;

            if (currentWeapon.gameObject.layer != layer)
            {
                // First person weapon is rendered differently (see ForwardRenderer asset)
                currentWeapon.gameObject.SetLayer(layer, true);
            }

            // Weapon is in different position for first person vs third person to align nicely in camera view
            //currentWeapon.transform.localPosition = offset;
        }

        private void SetWeaponViewLeft(int layer, Vector3 offset)
        {
            if (CurrentLeftWeapon == null)
                return;

            var currentWeapon = CurrentLeftWeapon;

            if (currentWeapon.gameObject.layer != layer)
            {
                // First person weapon is rendered differently (see ForwardRenderer asset)
                currentWeapon.gameObject.SetLayer(layer, true);
            }

            // Weapon is in different position for first person vs third person to align nicely in camera view
            // currentWeapon.transform.localPosition = offset;
        }

        private void AddWeapon(Weapon weapon, NetworkArray<Weapon> weaponList)
        {
            if (weapon == null)
                return;

            RemoveWeapon(weapon.WeaponSlot, weaponList);

            weapon.Object.AssignInputAuthority(Object.InputAuthority);

            weaponList.Set(weapon.WeaponSlot, weapon);
        }

        private void RemoveWeapon(int slot, NetworkArray<Weapon> weaponList, bool despawn = true)
        {
            try
            {
                // Skip weapon removal during scene transitions
                if (Runner != null && Runner.SceneManager != null && Runner.SceneManager.IsBusy)
                {
                    Debug.Log($"Skipping weapon removal during scene transition for slot {slot}");
                    return;
                }

                // Check if the runner is valid
                if (Runner == null || !Runner.IsRunning)
                {
                    Debug.LogWarning("Runner is null or not running during RemoveWeapon");
                    return;
                }

                // Check if the slot is valid
                if (slot < 0 || slot >= weaponList.Length)
                {
                    Debug.LogWarning($"Invalid weapon slot: {slot}");
                    return;
                }

                var weapon = weaponList[slot];
                if (weapon == null)
                    return;

                if (despawn)
                {
                    // Make sure the weapon object is valid before despawning
                    if (weapon.Object != null && weapon.Object.IsValid)
                    {
                        try
                        {
                            Runner.Despawn(weapon.Object);
                        }
                        catch (System.Exception ex)
                        {
                            Debug.LogWarning($"Error despawning weapon in slot {slot}: {ex.Message}");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"Cannot despawn weapon in slot {slot}: Object is null or invalid");
                    }
                }
                else
                {
                    // Make sure the weapon object is valid before removing input authority
                    if (weapon.Object != null && weapon.Object.IsValid)
                    {
                        try
                        {
                            weapon.Object.RemoveInputAuthority();
                        }
                        catch (System.Exception ex)
                        {
                            Debug.LogWarning($"Error removing input authority from weapon in slot {slot}: {ex.Message}");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"Cannot remove input authority from weapon in slot {slot}: Object is null or invalid");
                    }
                }

                // Set the slot to null
                try
                {
                    weaponList.Set(slot, null);
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"Error setting weapon slot {slot} to null: {ex.Message}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Error in RemoveWeapon for slot {slot}: {ex.Message}");
            }
        }

        public NetworkArray<Weapon> GetRightWeapons()
        {
            return _rightWeapons;
        }

        public NetworkArray<Weapon> GetLeftWeapons()
        {
            return _leftWeapons;
        }

        // Debug method to log available weapons
        public void LogAvailableWeapons()
        {
            Debug.Log("Available Right Weapons:");
            for (int i = 0; i < _rightWeapons.Length; i++)
            {
                if (_rightWeapons[i] != null)
                {
                    Debug.Log($"  Slot {i}: {_rightWeapons[i].name}");
                }
                else
                {
                    Debug.Log($"  Slot {i}: Empty");
                }
            }

            Debug.Log("Available Left Weapons:");
            for (int i = 0; i < _leftWeapons.Length; i++)
            {
                if (_leftWeapons[i] != null)
                {
                    Debug.Log($"  Slot {i}: {_leftWeapons[i].name}");
                }
                else
                {
                    Debug.Log($"  Slot {i}: Empty");
                }
            }
        }

        public Weapon GetWeaponFromSlot(int slot)
        {
            var weaponList = _rightWeapons;
            var weapon = weaponList[slot];
            if (weapon == null)
                return null;
            else return weapon;
        }


    }



}
