using UnityEngine;
using Fusion;
using System.Collections;

public class Grabber : MonoBehaviour
{
    [Tooltip("THIS GETS ASSGINED AT RUNTIME WHEN WE GRAB AN OBJECT")]
    public Grabbable grabbedObject;

    public HardwareRig rig;
    public HardwareHand hand;

    public Vector3 ungrabPosition;
    public Quaternion ungrabRotation;
    public Vector3 ungrabVelocity;
    public Vector3 ungrabAngularVelocity;

    public NetworkGrabber networkGrabber;
    public bool resetGrabInfo = false;

    Collider lastCheckedCollider;
    Grabbable lastCheckColliderGrabbable;
    GrabInfo _grabInfo;

    [Header("Distance Grab Settings")]
    public float grabDistance = 2.0f; // Max distance for raycast grab
    public LayerMask grabbableLayer; // Assign in Inspector to filter grabbable objects

    private Grabbable lastHighlighted; // Keep track of the last highlighted object
    private bool isCheckingDistanceGrab = true; // Allows enabling/disabling the coroutine

    public GrabInfo GrabInfo
    {
        get
        {
            if (resetGrabInfo)
                return default;

            if (grabbedObject)
            {
                _grabInfo.grabbedObjectId = grabbedObject.networkGrabbable.Id;
                _grabInfo.localPositionOffset = grabbedObject.localPositionOffset;
                _grabInfo.localRotationOffset = grabbedObject.localRotationOffset;
            }
            else
            {
                _grabInfo.grabbedObjectId = NetworkBehaviourId.None;
                _grabInfo.ungrabPosition = ungrabPosition;
                _grabInfo.ungrabRotation = ungrabRotation;
                _grabInfo.ungrabVelocity = ungrabVelocity;
                _grabInfo.ungrabAngularVelocity = ungrabAngularVelocity;
            }

            return _grabInfo;
        }
    }

    private void Awake()
    {
        Debug.Assert(hand, "ASSIGN NETWORK HAND TO GRABBER COMPONENT ON " + this.gameObject);
    }

    void Start()
    {
        StartCoroutine(RaycastGrabCheckLoop()); // Start the distance grab coroutine
    }

    public void SetHardwareRig(HardwareRig hardwareRig)
    {
        rig = hardwareRig;

    }

    private void OnTriggerStay(Collider other)
    {


        if (!CanGrab())
            return;

        Grabbable grabbable = lastCheckedCollider == other ? lastCheckColliderGrabbable : other.GetComponent<Grabbable>();

        lastCheckedCollider = other;
        lastCheckColliderGrabbable = grabbable;

        if (grabbable != null && grabbable.currentGrabber == null && hand.isGrabbing)
        {
            Grab(grabbable);
        }
    }

    private bool CanGrab()
    {
        // Early exit if rig, runner, or resimulation is invalid
        if (rig?.runner?.IsResimulation == true) return false;

        // Can't grab if already holding an object
        if (grabbedObject != null) return false;

        try
        {
            // Ensure rig and local player exist
            if (rig == null) return false;
            var player = rig.localPlayer;
            if (player == null) return false;

            // Check if player object is valid
            if (player.Object == null || !player.Object.IsValid) return false;

            // Safely get the agent
            var agent = player.ActiveAgent;
            if (agent == null) return false;

            // Make sure agent has weapons
            var weapons = agent.Weapons;
            if (weapons == null) return false;

            // Stop grabbing if a weapon is in hand
            bool isRightHandOccupied = (hand.side == RigPart.RightController && !weapons.IsRightHandEmpty());
            bool isLeftHandOccupied = (hand.side == RigPart.LeftController && !weapons.IsLeftHandEmpty());

            if (isRightHandOccupied || isLeftHandOccupied) return false;

            return true;
        }
        catch (System.Exception ex)
        {
            // Log the error but don't crash
            Debug.LogWarning($"Error in CanGrab: {ex.Message}");
            return false;
        }
    }

    private void Update()
    {
        if (rig && rig.runner && rig.runner.IsResimulation)
            return;


        if (grabbedObject != null && grabbedObject.currentGrabber != this)
        {
            grabbedObject = null;
        }

        if (grabbedObject != null && !hand.isGrabbing)
        {
            Ungrab(grabbedObject);
        }

    }

    private IEnumerator RaycastGrabCheckLoop()
    {
        while (isCheckingDistanceGrab)
        {
            RaycastGrab();
            yield return new WaitForSeconds(0.05f); // Frequent to check
        }
    }

    private void RaycastGrab()
    {

        if (!CanGrab())
            return;

        Ray ray = new Ray(hand.transform.position, hand.transform.forward);

        if (Physics.Raycast(ray, out RaycastHit hit, grabDistance, grabbableLayer))
        {

            Grabbable grabbable = hit.collider.GetComponent<Grabbable>();

            if (grabbable != null && grabbable.currentGrabber == null)
            {
                if (hand.isGrabbing)
                {
                    DistanceGrab(grabbable);
                }
                else
                {
                    if (grabbable != lastHighlighted)
                    {
                        if (lastHighlighted != null)
                            lastHighlighted.HideGrabberHighlight(); // Remove highlight from previous object

                        HighlightGrabbable(grabbable);
                        lastHighlighted = grabbable;
                    }
                }
            }
        }
        else
        {
            if (lastHighlighted != null)
            {
                lastHighlighted.HideGrabberHighlight();
                lastHighlighted = null;
            }
        }
    }

    // Call this if you ever need to stop checking for grabbables (e.g., during a pause menu)
    public void StopCheckingDistanceGrab()
    {
        isCheckingDistanceGrab = false;
    }

    public void StartCheckingDistanceGrab()
    {
        isCheckingDistanceGrab = true;
    }
    public void Grab(Grabbable grabbable)
    {
        // Debug.Log($"Try to grab object {grabbable.gameObject.name} with {gameObject.name}");
        if (grabbable.Grab(this))
        {
            grabbedObject = grabbable;
            
              if (hand.side == RigPart.RightController)
            {
                grabbedObject.localPositionOffset = grabbedObject.rightHandPositionOffset;
                grabbedObject.localRotationOffset = grabbedObject.rightHandRotationOffset;
            }
            else
            {
                grabbedObject.localPositionOffset = grabbedObject.leftHandPositionOffset;
                grabbedObject.localRotationOffset = grabbedObject.leftHandRotationOffset;
            }

        }
    }

    public void DistanceGrab(Grabbable grabbable)
    {
      //  Debug.Log($"Try to grab object {grabbable.gameObject.name} with {gameObject.name}");
        if (grabbable.Grab(this))
        {
            grabbedObject = grabbable;

            if (hand.side == RigPart.RightController)
            {
                grabbedObject.localPositionOffset = grabbedObject.rightHandPositionOffset;
                grabbedObject.localRotationOffset = grabbedObject.rightHandRotationOffset;
            }
            else
            {
                grabbedObject.localPositionOffset = grabbedObject.leftHandPositionOffset;
                grabbedObject.localRotationOffset = grabbedObject.leftHandRotationOffset;
            }

        }
    }

    public void HighlightGrabbable(Grabbable grabbable)
    {
        grabbable.ShowGrabberHighlight();
    }

    public void Ungrab(Grabbable grabbable)
    {
      //  Debug.Log($"Try to ungrab object {grabbable.gameObject.name} with {gameObject.name}");
        if (grabbable.networkGrabbable)
        {
            ungrabPosition = grabbedObject.networkGrabbable.transform.position;
            ungrabRotation = grabbedObject.networkGrabbable.transform.rotation;
            ungrabVelocity = grabbedObject.Velocity;

            ungrabAngularVelocity = grabbedObject.AngularVelocity;
        }
        grabbedObject.Ungrab();
        grabbedObject = null;
    }


}


