using Fusion;
using Projectiles;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;


public class AmmoPouch : InteractableObject
{

    // Utility method to ensure quaternions are properly normalized
    private Quaternion NormalizeQuaternion(Quaternion q)
    {
        // Check if the quaternion is already normalized (within a small epsilon)
        float magnitude = Mathf.Sqrt(q.x * q.x + q.y * q.y + q.z * q.z + q.w * q.w);

        // If the magnitude is close enough to 1, return the original quaternion
        if (Mathf.Abs(magnitude - 1f) < 0.0001f)
            return q;

        // Otherwise, normalize it
        return new Quaternion(
            q.x / magnitude,
            q.y / magnitude,
            q.z / magnitude,
            q.w / magnitude
        );
    }
    [SerializeField]
    private Weapons playerWeapons;

    private float ammoSpawnCooldown = 2f; // Half a second cooldown
    private double nextAllowedAmmoSpawnTime = -Mathf.Infinity;


    private void Awake()
    {
        Debug.Assert(playerWeapons, "ASSIGN WEAPONS.cs TO AMMO POUCH ATTACHED TO PLAYER OBJECT " + this.gameObject.name);
    }

    private bool isSpawningMagazine = false; // Track if a magazine is being initialized

    public void TryGetAmmo(NetworkHand hand)
    {
        if (Runner.SimulationTime < nextAllowedAmmoSpawnTime || isSpawningMagazine)
            return; // Prevents spamming & ensures previous magazine is initialized

        if (hand.networkGrabber.IsGrabbingAnObject())
            return;

        if (playerWeapons.IsLeftHandEmpty() && playerWeapons.IsRightHandEmpty())
            return;



        Weapon weapon;

        if (hand.side == RigPart.LeftController)
        {
            if (!playerWeapons.IsLeftHandEmpty()) return;
            if (playerWeapons.CurrentRightWeapon.weaponType != WeaponType.Ranged) return;

            weapon = playerWeapons.CurrentRightWeapon;
        }
        else
        {
            if (!playerWeapons.IsRightHandEmpty()) return;
            if (playerWeapons.CurrentLeftWeapon.weaponType != WeaponType.Ranged) return;

            weapon = playerWeapons.CurrentLeftWeapon;
        }

        if (weapon.weaponMagazine.WeaponAmmo <= 0)
            return;


        //If reload phase is already applied new mag, dont give a new one
        if (weapon.weaponAnimator.reloadPhase == 3)
            return;

     

        // Mark that a magazine is being spawned
        isSpawningMagazine = true;


        var localPreview = SpawnDummyMagzine(hand,weapon.weaponMagazine.dummyMagazinePrefab);


        // Request actual magazine from server/state authority
        if (Runner.IsServer || Object.HasStateAuthority)
        {
            SpawnMagazine(hand.ObjectSpawnPoint, weapon.weaponMagazine.physicalMagazine);
        }
        else
        {
            // Send RPC to authoritative player (likely host) to spawn
            RPC_RequestMagazine(hand.side);
        }


        StartCoroutine(WaitForMagazineAndReplace(localPreview, hand));


        nextAllowedAmmoSpawnTime = Runner.SimulationTime + ammoSpawnCooldown;
    }


    private IEnumerator WaitForMagazineAndReplace(GameObject localPreview, NetworkHand hand)
    {
        NetworkObject spawnedMag = null;
        float timeout = Time.time + 2f;

     
        while (Time.time < timeout)
        {
            spawnedMag = MagazineRegistry.Get(hand.playerAgent.Object.InputAuthority);
        
            if (spawnedMag != null)
                break;

            yield return null;
        }

        if (spawnedMag == null)
        {
            Debug.LogError("Timed out waiting for spawned magazine.");
            localPreview.transform.SetParent(null);

            // Replace local visual with real one
            _context.ObjectCache.Return(localPreview);
            yield break;
        }

        // Clean up registry
        MagazineRegistry.Unregister(hand.playerAgent.Object.InputAuthority);


        localPreview.transform.SetParent(null);

        // Replace local visual with real one
        _context.ObjectCache.Return(localPreview);

        if (spawnedMag.TryGetComponent(out WeaponPhysicalMagazine realMag))
        {


            if (hand.playerAgent.hardwareRig)
            {
                if (hand.side == RigPart.RightController)
                {
                    hand.playerAgent.hardwareRig.rightHand.grabber.Grab(realMag.networkPhysicsGrabbable.grabbable);
                }
                else
                {
                    hand.playerAgent.hardwareRig.leftHand.grabber.Grab(realMag.networkPhysicsGrabbable.grabbable);
                }


            }



        }
        else
        {
            Debug.LogWarning("Spawned magazine is missing WeaponPhysicalMagazine component.");
        }


        // Unlock spawning after successful initialization
        isSpawningMagazine = false;
    }



    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    public void RPC_RequestMagazine(RigPart handSide)
    {
        var player = Runner.GetPlayerObject(Object.InputAuthority); // Get correct player

        NetworkHand hand = null;

        if (handSide == RigPart.LeftController)
        {
            hand = player.GetComponent<PlayerAgent>().LeftNetworkHand();
        }
        else
        {
            hand = player.GetComponent<PlayerAgent>().RightNetworkHand();
        }

        SpawnMagazine(hand.ObjectSpawnPoint, hand.playerAgent.Weapons.CurrentLeftWeapon.weaponMagazine.physicalMagazine); // Call your existing logic
    }


    public NetworkObject SpawnMagazine(Transform handTransform, GameObject spawnableMagazine)
    {
        if (HasStateAuthority)
        {
            // Ensure we're using the most up-to-date hand position for spawning
            // Normalize the rotation to prevent quaternion errors
            Quaternion normalizedRotation = NormalizeQuaternion(handTransform.rotation);
            var magObject = Runner.Spawn(spawnableMagazine, handTransform.position, normalizedRotation, inputAuthority: Object.InputAuthority);

            MagazineRegistry.Register(magObject.InputAuthority, magObject);

            // Force update position to ensure it's at the exact hand position
            if (magObject != null)
            {
                magObject.transform.SetPositionAndRotation(handTransform.position, normalizedRotation);

                // Initialize the magazine with the player's NetworkId
                NetworkId playerNetworkId = Object.Id;

                // TryGetComponent doesn't allocate memory if component isn't found
                if (magObject.TryGetComponent(out WeaponPhysicalMagazine physicalMag))
                {
                    physicalMag.Init(playerNetworkId);
                }
            }



            return magObject; // Return the spawned object
        }
        return null;
    }

    private GameObject SpawnDummyMagzine(NetworkHand hand, GameObject dummyMagazine)
    {
        Transform handTransform = hand.ObjectSpawnPoint.transform;
        GameObject tempMagazineObject = null;


        // Create a temporary visual object that follows the hand
        // Ensure the rotation is normalized to prevent quaternion errors
        Quaternion normalizedRotation = NormalizeQuaternion(handTransform.rotation);
        tempMagazineObject = _context.ObjectCache.Get(dummyMagazine);
        tempMagazineObject.transform.SetParent(handTransform);
        tempMagazineObject.transform.SetPositionAndRotation(handTransform.position, normalizedRotation);


    
        return tempMagazineObject;
    }



  


}

public static class MagazineRegistry
{
    private static readonly Dictionary<PlayerRef, NetworkObject> pendingMags = new();

    public static void Register(PlayerRef playerRef, NetworkObject mag)
    {
        pendingMags[playerRef] = mag;
    }

    public static NetworkObject Get(PlayerRef playerRef)
    {
        if (!pendingMags.TryGetValue(playerRef, out var mag))
        {
            //Debug.LogError($"[MAG REGISTRY] No magazine registered for player: {playerRef}");
        }
        return mag;
    }

    public static void Unregister(PlayerRef playerRef)
    {
        pendingMags.Remove(playerRef);
    }
}