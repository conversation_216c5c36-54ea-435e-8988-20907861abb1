/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * Licensed under the Oculus SDK License Agreement (the "License");
 * you may not use the Oculus SDK except in compliance with the License,
 * which is provided at the time of installation or download, or which
 * otherwise accompanies this software in either electronic or hard copy form.
 *
 * You may obtain a copy of the License at
 *
 * https://developer.oculus.com/licenses/oculussdk/
 *
 * Unless required by applicable law or agreed to in writing, the Oculus SDK
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

using System;
using UnityEngine;
using System.Collections;
using JetBrains.Annotations;
using Meta.XR.Samples;
using UnityEngine.Assertions;
#if UNITY_EDITOR
using UnityEngine.SceneManagement;
#endif

/// <summary>
/// Simply aggregates accessors.
/// </summary>
[MetaCodeSample("StarterSample.Core-Locomotion")]
public class LocomotionController : MonoBehaviour
{
    public OVRCameraRig CameraRig;
    public CapsuleCollider CharacterController;
    public SimpleCapsuleWithStickMovement PlayerMovementController;
    public HardwareRig hardwareRig;
    public TeleportInputHandlerTouch telportInputHandler;
    public TeleportOrientationHandlerThumbstick teleportOrientationHandlerThumbstick;
    public OVRInput.Controller activePlayerMovementHand;
    public HardwareRigSettings rigSettings;

    private void OnEnable()
    {
        hardwareRig.OnLocomotionTypeChanged += ApplyLocomotion;
    }

    private void OnDisable()
    {
        hardwareRig.OnLocomotionTypeChanged -= ApplyLocomotion;
    }

    private void Start()
    {
        Debug.Assert(rigSettings != null, "RigSettings reference is missing in LocomotionController");
        ApplyLocomotion(hardwareRig.LocomotionType);
    }

    private void ApplyLocomotion(LocomotionType type)
    {
        if (type == LocomotionType.Teleportation)
        {
            EnableTeleportation();
            DisableJoyStickMovement();
        }
        else if (type == LocomotionType.Joystick)
        {
            EnableJoyStickMovement();
            DisableTeleportation();
        }

        // Save the locomotion type preference
        if (rigSettings != null)
        {
            rigSettings.SaveLocomotionType(type);
        }
    }

    public void EnableTeleportation()
    {
        // First enable the component
        telportInputHandler.enabled = true;
        
        // Force re-initialization of the teleport handler
        if (telportInputHandler.gameObject.activeInHierarchy)
        {
            telportInputHandler.gameObject.SetActive(false);
            telportInputHandler.gameObject.SetActive(true);
        }
        
        // Set the movement hand
        SetMovementHand(activePlayerMovementHand);
    }

    public void DisableTeleportation() 
    {
        telportInputHandler.enabled = false;
    }

    public void EnableJoyStickMovement()
    {
        PlayerMovementController.EnableLinearMovement = true;
        SetMovementHand(activePlayerMovementHand);
    }

    public void DisableJoyStickMovement()
    {
        PlayerMovementController.EnableLinearMovement = false;
    }


    public void SetMovementHand(OVRInput.Controller controller)
    {
        activePlayerMovementHand = controller;
       teleportOrientationHandlerThumbstick.Thumbstick = controller;
       telportInputHandler.AimingController = controller;
    }
}
