using Fusion.XR.Host.Utils;
using UnityEngine;

public class PhysicsGrabbable : Grabbable
{
    public Rigidbody rb;
    protected virtual void Awake()
    {
        networkGrabbable = GetComponent<NetworkGrabbable>();
        rb = GetComponent<Rigidbody>();
        rb.isKinematic = false;
        HideGrabberHighlight();
    }
    public override Vector3 Velocity => rb.linearVelocity;
    public override Vector3 AngularVelocity => rb.angularVelocity;
    public bool IsGrabbed => currentGrabber != null;

    #region Follow configuration
    [Header("Follow configuration")]
    [Range(0, 1)]
    public float followVelocityAttenuation = 0.5f;
    public float maxVelocity = 10f;

    public enum FollowMode
    {
        Velocity,
        PID
    }
    public FollowMode followMode = FollowMode.Velocity;

    [Header("PID")]
    public PIDState pid = new PIDState
    {
        pidSettings = new PIDSettings
        {
            proportionalGain = 0.75f,
            integralGain = 0.01f,
            derivativeGain = 0.12f,
            maxIntegrationMagnitude = 10f
        }
    };
    public float commandScale = 1.5f;
    public float maxCommandMagnitude = 100f;
    public bool ignorePidIntegrationWhileColliding = true;
    #endregion

    bool isCollidingOffline = false;

    #region Following logic

    public virtual void Follow(Transform followedTransform, float elapsedTime, bool isColliding)
    {

        if (followMode == FollowMode.PID)
        {
            PIDFollow(followedTransform, elapsedTime, isColliding);
        }
        else if (followMode == FollowMode.Velocity)
        {
            VelocityFollow(followedTransform, elapsedTime);
        }
    }

    // Optimization: Cache vectors to reduce GC pressure
    private Vector3 _cachedError = Vector3.zero;
    private Vector3 _cachedImpulse = Vector3.zero;

    // Distance threshold for optimizing PID calculations
    [Header("Performance Optimization")]
    public float significantDistanceThreshold = 0.001f; // Only apply forces if error is above this threshold
    public bool useAdaptivePID = true; // Dynamically adjust PID parameters based on distance

    public virtual void PIDFollow(Transform followedTransform, float elapsedTime, bool isColliding)
    {
        var targetPosition = followedTransform.TransformPoint(localPositionOffset);
        var targetRotation = followedTransform.rotation * localRotationOffset;

        // Calculate position error
        _cachedError = targetPosition - rb.position;

        // Skip tiny movements to reduce physics calculations
        if (_cachedError.sqrMagnitude < significantDistanceThreshold * significantDistanceThreshold)
        {
            // Still update rotation even if position is close enough
            rb.angularVelocity = rb.transform.rotation.AngularVelocityChange(newRotation: targetRotation, elapsedTime: elapsedTime);
            return;
        }

        // Adaptive PID: adjust parameters based on distance if enabled
        if (useAdaptivePID)
        {
            // Reduce integral gain when far away to prevent overshooting
            float distance = _cachedError.magnitude;
            if (distance > 0.1f)
            {
                pid.pidSettings.integralGain = 0.005f; // Lower for distant objects
            }
            else
            {
                pid.pidSettings.integralGain = 0.01f; // Default for close objects
            }
        }

        // Handle collision integration
        var ignoreIntegration = ignorePidIntegrationWhileColliding && isColliding;
        if (ignoreIntegration)
        {
            pid.errorIntegration = Vector3.zero;
        }

        // Calculate and apply force
        var command = pid.UpdateCommand(_cachedError, elapsedTime, ignoreIntegration: ignoreIntegration);
        _cachedImpulse = Vector3.ClampMagnitude(commandScale * command, maxCommandMagnitude);
        rb.AddForce(_cachedImpulse, ForceMode.Impulse);

        // Update rotation
        rb.angularVelocity = rb.transform.rotation.AngularVelocityChange(newRotation: targetRotation, elapsedTime: elapsedTime);
    }

    // Cached vectors for velocity calculations
    private Vector3 _cachedVelocity = Vector3.zero;

    public virtual void VelocityFollow(Transform followedTransform, float elapsedTime)
    {
        // Calculate target position and rotation
        Vector3 targetPosition = followedTransform.TransformPoint(localPositionOffset);
        Quaternion targetRotation = followedTransform.rotation * localRotationOffset;

        // Calculate position error
        Vector3 positionError = targetPosition - rb.position;

        // Skip tiny movements to reduce physics calculations
        if (positionError.sqrMagnitude < significantDistanceThreshold * significantDistanceThreshold)
        {
            // Still update rotation even if position is close enough
            rb.angularVelocity = rb.transform.rotation.AngularVelocityChange(newRotation: targetRotation, elapsedTime: elapsedTime);
            return;
        }

        // Compute velocity directly instead of using extension method to avoid GC
        _cachedVelocity = positionError / elapsedTime;

        // Apply attenuation and clamping
        _cachedVelocity *= followVelocityAttenuation;
        if (_cachedVelocity.sqrMagnitude > maxVelocity * maxVelocity)
        {
            _cachedVelocity = _cachedVelocity.normalized * maxVelocity;
        }

        // Apply velocity
        rb.linearVelocity = _cachedVelocity;
        rb.angularVelocity = rb.transform.rotation.AngularVelocityChange(newRotation: targetRotation, elapsedTime: elapsedTime);
    }
    #endregion

    // Collision tracking with optimization
    private int _collisionCount = 0;
    private float _lastCollisionTime = 0f;
    private const float COLLISION_MEMORY_TIME = 0.1f; // Remember collisions for 100ms

    private void FixedUpdate()
    {
        // We handle the following if we are not online (in that case, the Follow will be called by the NetworkGrabbable during FUN and Render)
        if (networkGrabbable == null || networkGrabbable.Object == null)
        {
            // Note that this offline following will not offer the pseudo-haptic feedback (it could easily be recreated offline if needed)
            if (IsGrabbed) Follow(followedTransform: currentGrabber.transform, Time.fixedDeltaTime, isColliding: isCollidingOffline);
        }

        // Reset collision state if enough time has passed since last collision
        if (Time.time - _lastCollisionTime > COLLISION_MEMORY_TIME)
        {
            isCollidingOffline = false;
            _collisionCount = 0;
        }
    }

    private void OnCollisionEnter(Collision collision)
    {
        _collisionCount++;
        _lastCollisionTime = Time.time;
        isCollidingOffline = true;
    }

    private void OnCollisionExit(Collision collision)
    {
        _collisionCount = Mathf.Max(0, _collisionCount - 1);
        if (_collisionCount == 0)
        {
            // Only set not colliding if we have no active collisions
            isCollidingOffline = false;
        }
    }

}
