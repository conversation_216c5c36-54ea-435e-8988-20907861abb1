using UnityEngine;
using Fusion;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// This script hands interating with pick up objects, different to Grabber
/// which is used for objects that we dont apply to players inventory
/// </summary>

public class HandsInteraction : NetworkBehaviour
{
    [SerializeField]
    private NetworkHand networkHand;

    [SerializeField]
    private InteractableObject currentItem;

    // Collision caching
    private Collider lastCheckedCollider;
    private InteractableObject lastCheckColliderItem;

    // Cooldown system
    [SerializeField]
    private float interactionCooldown = 0.1f; // 100ms cooldown between interaction checks
    private float lastInteractionAttemptTime = 0f;

    // Holster cooldown
    [SerializeField]
    private float holsterCooldown = 1f; // Cooldown duration in seconds
    private double holsterCooldownEndTime = -Mathf.Infinity; // Tracks when cooldown ends using network time

    // Layer filtering
    [SerializeField]
    private LayerMask interactableLayerMask = -1; // Default to all layers, set in inspector to filter

    // Component caching
    private InventoryItemPickUp cachedWeaponPickup; // Cache for the weapon pickup component
    private AmmoPouch cachedAmmoPouch; // Cache for the ammo pouch component
    private WeaponHolster cachedHolster; // Cache for the weapon holster component

    // Track interaction attempts per object to prevent excessive processing
    private Dictionary<InteractableObject, float> interactionAttemptTimes = new Dictionary<InteractableObject, float>();

    protected void Awake()
    {
        Debug.Assert(networkHand, "ASSIGN NETWORK HAND TO GRABBER COMPONENT ON " + this.gameObject);
    }

    private void OnEnable()
    {
        // Clear interaction tracking when object is enabled
        interactionAttemptTimes.Clear();
    }

    private void OnTriggerEnter(Collider other)
    {
        // Skip if we already have an item
        if (currentItem)
            return;

        // Check if the collision is with a relevant layer (if specified)
        if (interactableLayerMask != -1 && ((1 << other.gameObject.layer) & interactableLayerMask) == 0)
            return;

        // Global cooldown to prevent excessive checks
        if (Time.time - lastInteractionAttemptTime < interactionCooldown)
            return;

        TryInteractWithObject(other);
    }

    private void OnTriggerStay(Collider other)
    {
        // Skip if we already have an item
        if (currentItem)
            return;

        // Check if the collision is with a relevant layer (if specified)
        if (interactableLayerMask != -1 && ((1 << other.gameObject.layer) & interactableLayerMask) == 0)
            return;

        // Global cooldown to prevent excessive checks
        if (Time.time - lastInteractionAttemptTime < interactionCooldown)
            return;

        TryInteractWithObject(other);
    }

    private void TryInteractWithObject(Collider other)
    {
        lastInteractionAttemptTime = Time.time;

        InteractableObject item;

        // Use cached result if available
        if (lastCheckedCollider == other)
        {
            item = lastCheckColliderItem;
        }
        else
        {
            // Get the component (expensive operation)
            item = other.GetComponent<InteractableObject>();

            // Cache the result to avoid future GetComponent calls
            lastCheckedCollider = other;
            lastCheckColliderItem = item;
        }

        if (item != null)
        {
            // Check if we've recently tried to interact with this specific object
            if (interactionAttemptTimes.TryGetValue(item, out float lastAttemptTime))
            {
                // If we've tried recently with this specific object, skip
                if (Time.time - lastAttemptTime < 0.5f) // 500ms cooldown per object
                    return;
            }

            // Update the last attempt time for this object
            interactionAttemptTimes[item] = Time.time;

            // Set as current item
            currentItem = item;

            // Cache components based on interaction type for faster access later
            switch (item.interactionType)
            {
                case InteractionType.weaponPickUp:
                    cachedWeaponPickup = item.GetComponent<InventoryItemPickUp>();
                    break;
                case InteractionType.ammoPouch:
                    cachedAmmoPouch = item.GetComponent<AmmoPouch>();
                    break;
                case InteractionType.holster:
                    cachedHolster = item.GetComponent<WeaponHolster>();
                    break;
            }
        }
    }


    public override void FixedUpdateNetwork()
    {
        ProcessInteraction();
    }

    private void ProcessInteraction()
    {
        if (currentItem == null || !networkHand.isGrabbing) return;

        var isLeftHand = networkHand.side == RigPart.LeftController;

        // Global cooldown to prevent excessive processing
        if (Time.time - lastInteractionAttemptTime < interactionCooldown)
            return;

        lastInteractionAttemptTime = Time.time;

        switch (currentItem.interactionType)
        {
            case InteractionType.weaponPickUp:
                // If we don't have the component cached yet, try to get it
                if (cachedWeaponPickup == null)
                {
                    cachedWeaponPickup = currentItem.GetComponent<InventoryItemPickUp>();
                    if (cachedWeaponPickup == null) return;
                }

                networkHand.playerAgent.Inventory.HandleNewWeaponPickUp(cachedWeaponPickup.WeaponSlot, isLeftHand);

                //Check if Weapon pick up wants to be despawned after pick up & despawn if so
                if (cachedWeaponPickup.despawnAfterPickUp)
                {
                    if (HasStateAuthority)
                        Runner.Despawn(currentItem.Object);

                    cachedWeaponPickup = null;
                }
                break;

            case InteractionType.holster:
                // Only proceed if the holster cooldown has elapsed
                if (!IsHolsterOnCooldown())
                {
                    HandleHolsterInteraction(isLeftHand);
                    StartHolsterCooldown(); // Initiates networked cooldown
                }
                break;

            case InteractionType.ammoPouch:
                // If we don't have the component cached yet, try to get it
                if (cachedAmmoPouch == null)
                {
                    cachedAmmoPouch = currentItem.GetComponent<AmmoPouch>();
                    if (cachedAmmoPouch == null) return;
                }

                cachedAmmoPouch.TryGetAmmo(networkHand);
                break;
        }
    }



    private bool IsHolsterOnCooldown()
    {
        // Check if the current network time has passed the cooldown end time
        return Runner.SimulationTime < holsterCooldownEndTime;
    }

    private void StartHolsterCooldown()
    {
        // Set the cooldown end time using the current network time plus the cooldown duration
        holsterCooldownEndTime = Runner.SimulationTime + holsterCooldown;
    }
    private void EquipWeapon(bool isLeftHand, int weaponSlot)
    {
        if (isLeftHand)
            networkHand.playerAgent.Weapons.SwitchWeaponLeftHand(weaponSlot, true);
        else
            networkHand.playerAgent.Weapons.SwitchWeaponRightHand(weaponSlot, true);
    }

    private void HandleHolsterInteraction(bool isLeftHand)
    {
        // Use cached holster if available, otherwise get it
        if (cachedHolster == null)
        {
            cachedHolster = currentItem.GetComponent<WeaponHolster>();
        }

        if (cachedHolster == null) return;

        var weaponManager = networkHand.playerAgent.Weapons;

        if (cachedHolster.isStoringItem)
        {
            //Check if players hand is empty, if so, we want
            //to holster weapon in hands and equipp stored weapon
            //otherwise,
            //holster current weapon and set hands empty
            if (isLeftHand)
            {
                if (weaponManager.IsLeftHandEmpty())
                {//Gets stored item out of holster if nothing in players hands

                    EquipWeapon(isLeftHand, cachedHolster.storedWeaponSlot);
                    cachedHolster.RemoveItem();
                }
                else
                {
                    //Swap weapon in hands for holstered weapon
                    var weaponToStore = weaponManager.CurrentLeftWeaponSlot;

                    //Check wether this weapon can be holstered in this holster type
                    if (!cachedHolster.CanThisWeaponBeHolstered(weaponToStore))
                        return;


                    EquipWeapon(isLeftHand, cachedHolster.storedWeaponSlot);
                    cachedHolster.RemoveItem();
                    //Store new item
                    cachedHolster.StoreItem(weaponToStore);
                }
            }
            else
            {
                if (weaponManager.IsRightHandEmpty())
                {// Gets stored item out of holster if nothing in players hands
                    EquipWeapon(isLeftHand, cachedHolster.storedWeaponSlot);
                    cachedHolster.RemoveItem();
                }
                else
                { //Swap weapon in hands for holstered weapon
                    var weaponToStore = weaponManager.CurrentRightWeaponSlot;

                    //Check wether this weapon can be holstered in this holster type
                    if (!cachedHolster.CanThisWeaponBeHolstered(weaponToStore))
                        return;



                    EquipWeapon(isLeftHand, cachedHolster.storedWeaponSlot);
                    cachedHolster.RemoveItem();
                    //Store new item
                    cachedHolster.StoreItem(weaponToStore);
                }
            }


        }
        else
        {
            //Holsters players weapon,if nothing in players hands
            var currentWeaponSlot = isLeftHand ? weaponManager.CurrentLeftWeaponSlot : weaponManager.CurrentRightWeaponSlot;

            //Check wether this weapon can be holstered in this holster type
            if (!cachedHolster.CanThisWeaponBeHolstered(currentWeaponSlot))
                return;

            cachedHolster.StoreItem(currentWeaponSlot);
            EquipWeapon(isLeftHand, 0); // Unarmed
        }
    }

    private void OnTriggerExit(Collider other)
    {
        if (currentItem)
            RemoveInteractableObject();
    }

    public void RemoveInteractableObject()
    {
        // Clear all cached references
        currentItem = null;
        cachedWeaponPickup = null;
        cachedAmmoPouch = null;
        cachedHolster = null;
    }
}

