using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Threading.Tasks;
using Fusion;
using Fusion.Sockets;
using System;
using Projectiles;
using Unity.VisualScripting;

public enum RigPart
{
    None,
    Headset,
    LeftController,
    RightController,
    Undefined
}

public enum LocomotionType
{
    Teleportation,
    Joystick
}

public struct RigInput : INetworkInput
{
    public Vector3 playAreaPosition;
    public Quaternion playAreaRotation;
    public Vector3 leftHandPosition;
    public Quaternion leftHandRotation;
    public Vector3 rightHandPosition;
    public Quaternion rightHandRotation;
    public Vector3 headsetPosition;
    public Quaternion headsetRotation;
    public Vector3 leftHandVelocity;
    public Vector3 rightHandVelocity;
    //public HandCommand leftHandCommand;
    //public HandCommand rightHandCommand;
    public GrabInfo leftGrabInfo;
    public GrabInfo rightGrabInfo;
}

public struct CombinedInput : INetworkInput
{
    public RigInput rigInput;
    public GameplayInput gameplayInput;
}

public class HardwareRig : MonoBehaviour, INetworkRunnerCallbacks
{
    public HardwareHand leftHand;
    public HardwareHand rightHand;
    public Transform trackingArea;
    public OVRCameraRig headset;
    public HardwareRigSettings rigSettings;
    [SerializeField]
    private Rigidbody _rigidbody;
    [HideInInspector]
    public NetworkRunner runner;

    [HideInInspector]
    public Player localPlayer;

    bool searchingForRunner = false;

    [SerializeField]
    private Fader screenFader;

    [HideInInspector]
    public GameplayInput accumulatedInput;

    [Header("Locomotion")]    
    private LocomotionType _locomotionType;
    public event Action<LocomotionType> OnLocomotionTypeChanged;

    public LocomotionType LocomotionType
    {
        get => _locomotionType;
        set
        {
            if (_locomotionType != value)
            {
                _locomotionType = value;
                OnLocomotionTypeChanged?.Invoke(_locomotionType);
            }
        }
    }

    [Header("Height Calibration")]
    [SerializeField]
    private float realHeight;
    private float heightAdjustment; // Stores the current height adjustment value

   


    public enum RunnerExpectations
    {
        NoRunner, // For offline usages
        PresetRunner,
        DetectRunner // should not be used in multipeer scenario
    }

    private void Awake()
    {
        Debug.Assert(screenFader, "NO SCeeen fader attached to hardware rig " + this.gameObject.name);
        Debug.Assert(_rigidbody, "NO rigidbody attached to hardware rig " + this.gameObject.name);
    }

    protected void Start()
    {
        // await FindRunner();
        if (runner)
        {
            runner.AddCallbacks(this);

        }

      //  LocomotionType = LocomotionType.Teleportation;
    }


    //This gets called by controlled player agent on spawned
    public void SetRunner(NetworkRunner playerRunner, PlayerAgent agent)
    {
        runner = playerRunner;
        runner.AddCallbacks(this);


        StartCoroutine(WaitForPlayer(agent));
    }

    private IEnumerator WaitForPlayer(PlayerAgent playerAgent)
    {
        while (playerAgent .Owner == null) // Wait until owner is set
        {
            yield return null; // Wait for the next frame
        }

        SetLocalPlayer(playerAgent.Owner);
    }


    public void SetLocalPlayer(Player player)
    {
        // Get refernce to local player to can be used by localhardware scripts e.g. Grabber.cs
        localPlayer = player;
        //Set the network grabber for each local grabber
        leftHand.grabber.networkGrabber = player.ActiveAgent.LeftNetworkHand().networkGrabber;
        rightHand.grabber.networkGrabber = player.ActiveAgent.RightNetworkHand().networkGrabber;
    }

    public void MoveHardWareRig(Vector3 pos)
    {
      
        transform.position = pos;

        //Apply Rig settings
        rigSettings.InitRigSettings();

        StartCoroutine(FadeOut());
    }

    private void OnDestroy()
    {
        if (searchingForRunner) Debug.LogError("Cancel searching for runner in HardwareRig");
        searchingForRunner = false;
        if (runner) runner.RemoveCallbacks(this);
    }

    public virtual IEnumerator FadeOut()
    {
        if (screenFader) yield return screenFader.FadeOut();
    }

    public virtual IEnumerator FadeIn()
    {
        if (screenFader) yield return screenFader.FadeIn();
    }

    public virtual IEnumerator FadedTeleport(Vector3 position)
    {
        if (screenFader) yield return screenFader.FadeIn();
        //Teleport(position);
        if (screenFader) yield return screenFader.WaitBlinkDuration();
        if (screenFader) yield return screenFader.FadeOut();
    }

    // Rotate the rig with a fader
    public virtual IEnumerator FadedRotate()
    {
        if (screenFader) yield return screenFader.FadeIn();
        if (screenFader) yield return screenFader.WaitBlinkDuration();
        if (screenFader) yield return screenFader.FadeOut();
    }

  


    public void OnInput(NetworkRunner runner, NetworkInput input)
    {

        accumulatedInput.ButtonsLeft.Set(EInputButton.Grab, leftHand.isGrabbing);
        accumulatedInput.ButtonsRight.Set(EInputButton.Grab, rightHand.isGrabbing);
        CombinedInput combinedInput = new CombinedInput
        {
            rigInput = new RigInput
            {
                leftHandPosition = leftHand.transform.position,
                leftHandRotation = leftHand.transform.rotation,
                rightHandPosition = rightHand.transform.position,
                rightHandRotation = rightHand.transform.rotation,
                headsetPosition = headset.centerEyeAnchor.position,
                headsetRotation = headset.centerEyeAnchor.rotation,
                leftHandVelocity = OVRInput.GetLocalControllerVelocity(OVRInput.Controller.LTouch),
                rightHandVelocity = OVRInput.GetLocalControllerVelocity(OVRInput.Controller.RTouch),
                leftGrabInfo = leftHand.grabber.GrabInfo,
                rightGrabInfo = rightHand.grabber.GrabInfo,
                playAreaPosition = trackingArea.position,
                playAreaRotation = trackingArea.rotation    

            },
            gameplayInput = accumulatedInput,
            
           
        };


        input.Set(combinedInput);
    }
  
    public float AdjustPlayerHeight()
    {
        if (OVRManager.instance != null)
        {
            realHeight = headset.centerEyeAnchor.localPosition.y; // Get real-world user height
            heightAdjustment = -((realHeight - 1.7f) + 1); // Adjust based on 1.7m expected height

            Vector3 localPos = headset.transform.localPosition;
            localPos.y = heightAdjustment;
            headset.transform.localPosition = localPos;

            return heightAdjustment; // Return the adjustment value for saving
        }
        return 0f;
    }

    // Apply a previously saved height adjustment
    public void SetSavedHeightAdjustment(float savedAdjustment)
    {
        heightAdjustment = savedAdjustment;
        Vector3 localPos = headset.transform.localPosition;
        localPos.y = heightAdjustment;
        headset.transform.localPosition = localPos;
        Debug.Log("Applied saved height adjustment: " + heightAdjustment);
        
    }

   

    public void OnPlayerJoined(NetworkRunner runner, PlayerRef player) { }
    public void OnPlayerLeft(NetworkRunner runner, PlayerRef player) { }

    public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input) { }
    public void OnShutdown(NetworkRunner runner, ShutdownReason shutdownReason) { }
    public void OnConnectedToServer(NetworkRunner runner) { }
    public void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason) { }
    public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token) { }
    public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason) { }
    public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message) { }
    public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList) { }
    public void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data) { }
    public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken) { }
    public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ArraySegment<byte> data) { }
    public void OnSceneLoadDone(NetworkRunner runner) { }
    public void OnSceneLoadStart(NetworkRunner runner) { }
    public void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
    public void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
    public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data) { }
    public void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress) { }
}
