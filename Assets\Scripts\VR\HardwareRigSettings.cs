using UnityEngine;
using Projectiles;

public class HardwareRigSettings : MonoBehaviour
{
    public HardwareRig hardwareRig;
    [SerializeField]
    private InGameUIHolder uiHolder;
    public LocomotionController locomotionController;

    // Key used for PlayerPrefs
    private const string PLAYER_HEIGHT_KEY = "PlayerCameraHeightAdjustment";
    private const string LOCOMOTION_TYPE_KEY = "PlayerLocomotionType";
    private const string PLAYER_BELT_Y_OFFSET_KEY = "PlayerBeltYOffset";
    private const string PLAYER_BELT_Z_OFFSET_KEY = "PlayerBeltZOffset";

    [SerializeField]
    private float defaultBeltYOffset = -0.55f;
        [SerializeField]
    private float defaultBeltZOffset = -0.0f;


    private void Awake()
    {
        Debug.Assert(hardwareRig, "No hardwareRig.cs attached to HardwareRigSettings.cs on " + gameObject.name);
        Debug.Assert(uiHolder, "No uiHolder.cs attached to HardwareRigSettings.cs on " + gameObject.name);
        Debug.Assert(locomotionController, "No locomotionController.cs attached to HardwareRigSettings.cs on " + gameObject.name);

     //  PlayerPrefs.DeleteAll();

        // Load locomotion type immediately in Awake
        LoadLocomotionType();
    }

    public void InitRigSettings()
    {
        // Load saved locomotion type first
        LoadLocomotionType();

        // Then load saved height
        bool hasHeightSaved = LoadPlayerHeight();

        // Find the local player agent to apply belt position settings
        PlayerAgent localPlayerAgent = FindLocalPlayerAgent();
        if (localPlayerAgent != null)
        {
            // Load belt position offsets
            LoadBeltPositionOffsets(localPlayerAgent);
        }

        // Update UI elements
        uiHolder.InitializeUI();

        if (!hasHeightSaved)
        {
            //Show Calibrate Height UI
            uiHolder.ShowCalibrateHeightUI();
            uiHolder.ShowGamePanel();
        }
        else
        {
            uiHolder.HideCalibrateHeightUI();
            uiHolder.HideGamePanel();
        }
    }

    // Helper method to find the local player agent
    public PlayerAgent FindLocalPlayerAgent()
    {
        // Check if we have a valid hardware rig and local player
        if (hardwareRig == null || hardwareRig.localPlayer == null)
            return null;

        // Get the active agent directly from the local player
        PlayerAgent agent = hardwareRig.localPlayer.ActiveAgent;

        // Verify the agent is valid and is the local network player
        if (agent != null && agent.IsLocalNetworkPlayer)
        {
            return agent;
        }

        return null;
    }

    private void Update()
    {
        if (OVRInput.GetDown(OVRInput.Button.Start)) // Oculus menu button
        {

            uiHolder.ToggleSettingsPanel();
        }
    }

    public void SetPlayerMovementHand()
    {
        //0 is left hand and 1 is right hand
        if (locomotionController.activePlayerMovementHand == OVRInput.Controller.RTouch)
        {
            locomotionController.PlayerMovementController.leftStickMovement = true;
            locomotionController.SetMovementHand(OVRInput.Controller.LTouch);

        }
        else
        {
            locomotionController.PlayerMovementController.leftStickMovement = false;
            locomotionController.SetMovementHand(OVRInput.Controller.RTouch);

        }
    }

    public void SetSnapTurnHand()
    {
       if(locomotionController.PlayerMovementController.leftStickSnap)
        {
            locomotionController.PlayerMovementController.leftStickSnap = false;
        }
       else
            locomotionController.PlayerMovementController.leftStickSnap = true;
    }

    // Save the player's height adjustment value
    public void SavePlayerHeight(float adjustmentValue)
    {
        PlayerPrefs.SetFloat(PLAYER_HEIGHT_KEY, adjustmentValue);
        PlayerPrefs.Save();
        Debug.Log("Saved player height adjustment: " + adjustmentValue);
    }

    // Load previously saved player height
    public bool LoadPlayerHeight()
    {

        if (PlayerPrefs.HasKey(PLAYER_HEIGHT_KEY))
        {
            float savedAdjustment = PlayerPrefs.GetFloat(PLAYER_HEIGHT_KEY);
            hardwareRig.SetSavedHeightAdjustment(savedAdjustment);
            Debug.Log("Loaded saved height adjustment: " + savedAdjustment);
            return true;
        }
        else
        {
            Debug.Log("<color=yellow>No saved player height found</color>");
            return false;
        }
    }

    // Call this method when the user presses the calibrate button
    public void CalibratePlayerHeight()
    {
        float adjustmentValue = hardwareRig.AdjustPlayerHeight();
        SavePlayerHeight(adjustmentValue);
    }

    // Save the locomotion type preference
    public void SaveLocomotionType(LocomotionType type)
    {
        PlayerPrefs.SetInt(LOCOMOTION_TYPE_KEY, (int)type);
        PlayerPrefs.Save();
    }

    // Load previously saved locomotion type
    public void LoadLocomotionType()
    {
        if (PlayerPrefs.HasKey(LOCOMOTION_TYPE_KEY))
        {
            int savedType = PlayerPrefs.GetInt(LOCOMOTION_TYPE_KEY);
            LocomotionType type = (LocomotionType)savedType;
            hardwareRig.LocomotionType = type;
        }
        else
        {
            // Default to Teleportation if no preference is saved
            hardwareRig.LocomotionType = LocomotionType.Teleportation;
        }
    }

    // Save the player's belt Y position offset
    public void SaveBeltYOffset(float yOffset)
    {
        PlayerPrefs.SetFloat(PLAYER_BELT_Y_OFFSET_KEY, yOffset);
        PlayerPrefs.Save();
      
    }

    // Save the player's belt Z position offset
    public void SaveBeltZOffset(float zOffset)
    {
        PlayerPrefs.SetFloat(PLAYER_BELT_Z_OFFSET_KEY, zOffset);
        PlayerPrefs.Save();
       
    }

    // Load previously saved belt position offsets
    public void LoadBeltPositionOffsets(PlayerAgent playerAgent)
    {
        // Default values if no saved preferences exist
        float yOffset = defaultBeltYOffset; // Default value
        float zOffset = defaultBeltZOffset;     // Default value 

        // Load Y offset if it exists
        if (PlayerPrefs.HasKey(PLAYER_BELT_Y_OFFSET_KEY))
        {
            yOffset = PlayerPrefs.GetFloat(PLAYER_BELT_Y_OFFSET_KEY);
          
        }

        // Load Z offset if it exists
        if (PlayerPrefs.HasKey(PLAYER_BELT_Z_OFFSET_KEY))
        {
            zOffset = PlayerPrefs.GetFloat(PLAYER_BELT_Z_OFFSET_KEY);
           
        }

        // Apply the offsets to the player agent
        if (playerAgent != null)
        {
            playerAgent.SetBeltOffsets(yOffset, zOffset);
        }
    }

    // Adjust belt Y position by a delta amount
    public void AdjustBeltYPosition(float delta, PlayerAgent playerAgent = null)
    {
        // If no player agent is provided, try to find the local player agent
        if (playerAgent == null)
        {
            playerAgent = FindLocalPlayerAgent();
        }

        if (playerAgent != null)
        {
            float currentYOffset = playerAgent.GetBeltYOffset();
            float newYOffset = currentYOffset + delta;
            playerAgent.SetBeltYOffset(newYOffset);
            SaveBeltYOffset(newYOffset);
        }
    }

    // Adjust belt Z position by a delta amount
    public void AdjustBeltZPosition(float delta, PlayerAgent playerAgent = null)
    {
        // If no player agent is provided, try to find the local player agent
        if (playerAgent == null)
        {
            playerAgent = FindLocalPlayerAgent();
        }

        if (playerAgent != null)
        {
            float currentZOffset = playerAgent.GetBeltZOffset();
            float newZOffset = currentZOffset + delta;
            playerAgent.SetBeltZOffset(newZOffset);
            SaveBeltZOffset(newZOffset);
        }
    }
}
