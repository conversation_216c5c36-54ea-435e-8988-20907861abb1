using Fusion;
using Projectiles;
using System.Collections;
using UnityEngine;
using UnityEngine.InputSystem.XR;

public class HardwareHand : MonoBehaviour
{
    public RigPart side;

    public bool isGrabbing = false;
    public Grabber grabber;
    public NetworkTransform networkTransform;

    private OVRInput.Controller Controller;
    private void Awake()
    {
        if (side == RigPart.LeftController)
            Controller = OVRInput.Controller.LTouch;
        else
            Controller = OVRInput.Controller.RTouch;
    }

    private void Update()
    {
        
        if(side == RigPart.LeftController)
        {
            //Grip input
            float gripRight = OVRInput.Get(OVRInput.RawAxis1D.LHandTrigger);

            if (gripRight > 0.5f)
            {
                isGrabbing = true;
            }
            else
                isGrabbing = false;

        }
        else if(side == RigPart.RightController)
        {
            //Grip input
            float gripRight = OVRInput.Get(OVRInput.RawAxis1D.RHandTrigger);

            if (gripRight > 0.5f)
            {
                isGrabbing = true;
            }
            else
                isGrabbing = false;
        }
    }
    private Coroutine vibrationCoroutine;

    public void StartVibration(float vibrationFrequency, float vibrationAmplitude, float vibrationDuration)
    {   
        if (vibrationCoroutine == null)
        {
            vibrationCoroutine = StartCoroutine(VibrateController(vibrationFrequency, vibrationAmplitude, vibrationDuration,Controller));
        }
    }

    public void StopVibration()
    {
        if (vibrationCoroutine != null)
        {
            StopCoroutine(vibrationCoroutine);
            vibrationCoroutine = null;
        }
        OVRInput.SetControllerVibration(0, 0, Controller); // Stop vibration
    }

    private IEnumerator VibrateController(float vibrationFrequency, float vibrationAmplitude, float vibrationDuration, OVRInput.Controller controller)
    {
        OVRInput.SetControllerVibration(vibrationFrequency, vibrationAmplitude, controller);
        yield return new WaitForSeconds(vibrationDuration);
        StopVibration();
    }

    public void ApplyVibration(float frequency, float intensity)
    {
        OVRInput.SetControllerVibration(frequency, intensity, Controller);
    }
}
