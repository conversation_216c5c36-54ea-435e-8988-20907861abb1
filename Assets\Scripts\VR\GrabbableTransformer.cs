using UnityEngine;


public class GrabbableTransformer : MonoBehaviour
{
    [Header("Position Constraints")]
    public bool ConstrainX =false;
    public Vector2 XAxis= new Vector2(0,0);
    public bool ConstrainY = false;
    public Vector2 YAxis = new Vector2(0, 0);
    public bool ConstrainZ= false;
    public Vector2 ZAxis = new Vector2(0, 0);
    public bool isChild = false;

    [Header("Grabbing Hand Positons")]
    public Vector3 handPositionOffset = new Vector3();
    public Vector3 handRotationOffset = new Vector3();


   
}
