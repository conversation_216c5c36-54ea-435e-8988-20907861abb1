using UnityEngine;
using Fusion;
using Projectiles;

/// <summary>
/// Base class for an item pick up that the player can interact with
/// </summary>

public enum InteractionType
{
    weaponPickUp, // Player picks up and immedlaty equipps weapon
    holster, // Player puts current weapon away and swaps to empty hand, and vice versa.
    ammoPouch, //Player interacts with to pick up ammo for current weapon
}

public class InteractableObject : NetworkBehaviour
{
    public InteractionType interactionType;

    protected SceneContext _context;

    [Tooltip("If the player hand needs to be empty before can interact with object")]
    public bool requiresEmptyHand = false;

    public override void Spawned()
    {
        //Get a refernce to the scene so we can access the pooling system
        var scene = Runner.SimulationUnityScene.GetComponent<Scene>(true);
        if (scene)
        {
            _context = scene.Context;
        }
    }
}
