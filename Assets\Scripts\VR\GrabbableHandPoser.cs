using UnityEngine;

public class GrabbableHandPoser : Mono<PERSON><PERSON><PERSON><PERSON>
{
    [SerializeField,Tooltip("The grabbale object this component is attached to")]
    private NetworkGrabbable grabbable;


    [SerializeField]
    private GameObject _leftHandPose;
    [SerializeField] 
    private GameObject _rightHandPose;

    //This is the mesh of the actauly hand mesh attached to our player character
    private NetworkHand hand;

    private void Awake()
    {
        Debug.Assert(grabbable, "Assign Grabbable object to Grabbale hand poser " + this.gameObject.name);
        DisableHandPoses();
    }

    private void OnEnable()
    {
        DisableHandPoses();

    }


    private void DisableHandPoses()
    {
        _leftHandPose.SetActive(false);
        _rightHandPose.SetActive(false);
        hand = null;


    }

    public void ShowHandPose()
    {
        if(grabbable && grabbable.CurrentGrabber)
        {
           
            if (grabbable.CurrentGrabber.hand.side == RigPart.LeftController)
            {
                _leftHandPose.SetActive(true);
             


            }
            else if (grabbable.CurrentGrabber.hand.side == RigPart.RightController)
            {
                _rightHandPose.SetActive(true);
             


            }
            //Turned this off while we are doing the whole grabber reworking
            hand = grabbable.CurrentGrabber.hand;

            if(hand) 
                hand.TurnOffHandMesh();
        }
    }

    public void HideHandPose()
    {
        if (hand)
        {
            hand.TurnOnHandMesh();
        }
            DisableHandPoses();
    }

    
}
