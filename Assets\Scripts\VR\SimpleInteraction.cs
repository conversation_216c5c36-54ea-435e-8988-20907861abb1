using UnityEngine;
using Fusion;
using Projectiles;
using System;

[Obsolete("Obsolete! Was for inital pick up testing", true)]
public class SimpleInteraction : NetworkBehaviour
{

    [SerializeField]
    private PlayerAgent _agent;

    [SerializeField]
    private SimpleWeaponPickup interactableObject;

    public override void FixedUpdateNetwork()
    {
        ProcessInteraction();
    }


    private void ProcessInteraction()
    {
        if (GetInput(out CombinedInput inputRight))
        {
          if(inputRight.gameplayInput.ButtonsRight.WasPressed(_agent.Input.PreviousButtonsRight, 4))
            {
                if(interactableObject)
                {


                    _agent.Weapons.SwitchWeaponRightHand(interactableObject.WeaponSlot, true);

                }
               
            }

            if (inputRight.gameplayInput.ButtonsLeft.WasPressed(_agent.Input.PreviousButtonsLeft, 4))
            {
                if (interactableObject)
                {
                  
                    _agent.Weapons.SwitchWeaponLeftHand(interactableObject.WeaponSlot, true);


                }

            }
        }

    }

    public void SetInteractableObject(SimpleWeaponPickup item)
    {
        interactableObject = item;
    }

    public void RemoveInteractableObject()
    {
        interactableObject = null;
    }
}
